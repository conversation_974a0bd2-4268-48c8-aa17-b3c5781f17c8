<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>梵素EAP仓库管理系统详细设计方案（增强版）</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 25px;
        }
        h4 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        .toc {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .module-card {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .workflow {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 15px 0;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .process-flow {
            background-color: #f0f8ff;
            border: 2px solid #4169e1;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .validation-box {
            background-color: #f0fff0;
            border: 2px solid #32cd32;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .cost-box {
            background-color: #fff8dc;
            border: 2px solid #ffd700;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .ui-design-section {
            background-color: #f0f4ff;
            border: 2px solid #5168e0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .ui-component {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>梵素EAP仓库管理系统详细设计方案（增强版）</h1>
        
        <div class="toc">
            <h3>目录</h3>
            <ul>
                <li><a href="#overview">1. 系统概述</a></li>
                <li><a href="#model-validation">2. 数据模型验证与优化</a></li>
                <li><a href="#cost-module">3. 成本核算功能设计</a></li>
                <li><a href="#modules">4. 功能模块详细设计</a></li>
                <li><a href="#workflow">5. 业务流程详细设计</a></li>
                <li><a href="#ui-design">6. 业务UI界面详细设计</a></li>
                <li><a href="#validation">7. 业务流程逻辑完整性校验</a></li>
                <li><a href="#technology">8. 技术架构</a></li>
                <li><a href="#implementation">9. 实施方案</a></li>
            </ul>
        </div>

        <section id="overview">
            <h2>1. 系统概述</h2>
            
            <h3>1.1 项目背景</h3>
            <p>梵素EAP仓库管理系统是基于.NET 8.0和Blazor技术栈开发的现代化智能仓储管理解决方案。系统采用微服务架构，支持多仓库、多租户管理，提供从入库到出库的全流程数字化管控。经过对现有数据模型的全面验证，系统已具备完整的仓库管理功能，本次增强版重点新增成本核算功能，并为每个业务点提供详细的UI界面设计。</p>
            
            <h3>1.2 核心价值</h3>
            <ul>
                <li><strong>数字化转型</strong>：全面数字化仓储作业流程，提升管理效率</li>
                <li><strong>智能化决策</strong>：基于大数据分析的智能库存优化和预测</li>
                <li><strong>精细化管理</strong>：精确到库位级别的库存管理和追溯</li>
                <li><strong>成本核算</strong>：<span class="highlight">实时成本追踪、多维度成本分析、成本异常预警</span></li>
                <li><strong>标准化作业</strong>：标准化作业流程，降低人为错误</li>
                <li><strong>可视化监控</strong>：实时可视化监控，快速响应异常</li>
            </ul>

            <h3>1.3 系统架构优势</h3>
            <ul>
                <li><strong>基于现有模型验证</strong>：系统已有完整的Warehouse、Inventory、Inbound、Outbound等核心模型</li>
                <li><strong>成本分析模型完备</strong>：包含CostAnalysisData、CostStatistics、CostTrendData等成本分析模型</li>
                <li><strong>枚举定义全面</strong>：40+枚举文件定义了所有业务状态和类型</li>
                <li><strong>多语言支持</strong>：通过Localization实现国际化</li>
                <li><strong>审计追踪</strong>：BasePoco基类提供完整的审计字段</li>
            </ul>
        </section>

        <section id="model-validation">
            <h2>2. 数据模型验证与优化</h2>
            
            <div class="validation-box">
                <h3>2.1 现有模型结构验证</h3>
                
                <h4>2.1.1 核心模型验证结果</h4>
                <table>
                    <thead>
                        <tr>
                            <th>模型类别</th>
                            <th>主要实体</th>
                            <th>验证结果</th>
                            <th>优化建议</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>仓库管理</td>
                            <td>Warehouse、WarehouseArea、Location</td>
                            <td>✅ 完整</td>
                            <td>已包含成本字段，无需修改</td>
                        </tr>
                        <tr>
                            <td>物料管理</td>
                            <td>Material、MaterialCategory、MaterialBarcode</td>
                            <td>✅ 完整</td>
                            <td>StandardCost字段已存在</td>
                        </tr>
                        <tr>
                            <td>库存管理</td>
                            <td>Inventory、InventoryMovement、InventoryLedger</td>
                            <td>✅ 完整</td>
                            <td>Cost字段已存在，支持成本核算</td>
                        </tr>
                        <tr>
                            <td>入库管理</td>
                            <td>Inbound、InboundOrder、InboundOrderDetail</td>
                            <td>✅ 完整</td>
                            <td>UnitPrice、TotalAmount字段完备</td>
                        </tr>
                        <tr>
                            <td>出库管理</td>
                            <td>Outbound、OutboundOrder、OutboundOrderDetail</td>
                            <td>✅ 完整</td>
                            <td>包含价格字段，支持成本计算</td>
                        </tr>
                        <tr>
                            <td>质检管理</td>
                            <td>QualityInspection、QualityInspectionDetail</td>
                            <td>✅ 完整</td>
                            <td>流程完整，无需修改</td>
                        </tr>
                        <tr>
                            <td>盘点管理</td>
                            <td>StockTaking、StockTakingDetail</td>
                            <td>✅ 完整</td>
                            <td>支持成本差异调整</td>
                        </tr>
                        <tr>
                            <td>成本分析</td>
                            <td>CostAnalysisData、CostStatistics、CostTrendData</td>
                            <td>✅ 完整</td>
                            <td>成本分析模型齐全</td>
                        </tr>
                    </tbody>
                </table>

                <h4>2.1.2 关键字段验证</h4>
                <ul>
                    <li><strong>Inventory.Cost</strong>：库存成本字段，decimal(18,4)精度，支持精确成本核算</li>
                    <li><strong>Material.StandardCost</strong>：物料标准成本，decimal(18,2)精度</li>
                    <li><strong>Inbound.UnitPrice/TotalAmount</strong>：入库单价和总金额，支持采购成本核算</li>
                    <li><strong>CostAnalysisData</strong>：包含CurrentCost、StandardCost、CostDifference等成本分析字段</li>
                    <li><strong>CostStatistics</strong>：提供成本统计分析，包含最高/最低/平均成本等</li>
                </ul>
            </div>
        </section>

        <section id="cost-module">
            <h2>3. 成本核算功能设计</h2>
            
            <div class="cost-box">
                <h3>3.1 成本核算体系架构</h3>
                
                <h4>3.1.1 成本构成要素</h4>
                <table>
                    <thead>
                        <tr>
                            <th>成本类型</th>
                            <th>成本要素</th>
                            <th>计算方法</th>
                            <th>数据来源</th>
                            <th>更新频率</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>采购成本</td>
                            <td>物料采购价格、运输费用、税费</td>
                            <td>采购单价 × 数量 + 附加费用</td>
                            <td>Inbound.UnitPrice</td>
                            <td>每次入库</td>
                        </tr>
                        <tr>
                            <td>仓储成本</td>
                            <td>仓库租金、人工费、设备折旧</td>
                            <td>固定成本分摊 + 变动成本</td>
                            <td>WarehouseKPI</td>
                            <td>每月计算</td>
                        </tr>
                        <tr>
                            <td>作业成本</td>
                            <td>入库、出库、移库作业成本</td>
                            <td>作业时间 × 人工成本率</td>
                            <td>WarehouseTask</td>
                            <td>每次作业</td>
                        </tr>
                        <tr>
                            <td>质检成本</td>
                            <td>检验人工、设备使用、耗材</td>
                            <td>检验时间 × 成本率 + 耗材成本</td>
                            <td>QualityInspection</td>
                            <td>每次质检</td>
                        </tr>
                        <tr>
                            <td>损耗成本</td>
                            <td>破损、过期、盘亏损失</td>
                            <td>损耗数量 × 单位成本</td>
                            <td>StockTaking</td>
                            <td>每次盘点</td>
                        </tr>
                    </tbody>
                </table>

                <h4>3.1.2 成本核算方法</h4>
                <div class="workflow">
                    <h5>移动加权平均成本法</h5>
                    <div class="code-block">
// 成本计算服务
public class CostCalculationService
{
    /// <summary>
    /// 计算移动加权平均成本
    /// </summary>
    public decimal CalculateWeightedAverageCost(string materialId, string warehouseId)
    {
        // 获取当前库存
        var currentInventory = _inventoryRepo.GetByMaterialAndWarehouse(materialId, warehouseId);
        var currentQuantity = currentInventory.Sum(i => i.Quantity);
        var currentTotalCost = currentInventory.Sum(i => i.Quantity * i.Cost);
        
        // 获取新入库记录
        var newInbound = _inboundRepo.GetLatestInbound(materialId, warehouseId);
        var newQuantity = newInbound.Quantity;
        var newCost = newInbound.UnitPrice;
        
        // 计算加权平均成本
        var totalQuantity = currentQuantity + newQuantity;
        var totalCost = currentTotalCost + (newQuantity * newCost);
        
        return totalQuantity > 0 ? totalCost / totalQuantity : 0;
    }
    
    /// <summary>
    /// 计算仓储成本分摊
    /// </summary>
    public decimal CalculateStorageCost(string locationId, DateTime startDate, DateTime endDate)
    {
        // 获取库位信息
        var location = _locationRepo.GetById(locationId);
        var warehouseCost = _warehouseRepo.GetMonthlyCost(location.WarehouseId);
        
        // 计算库位占用比例
        var locationRatio = location.Area / location.Warehouse.TotalArea;
        
        // 计算时间占用
        var days = (endDate - startDate).Days;
        var monthlyDays = 30;
        
        // 分摊成本
        return warehouseCost * locationRatio * (days / monthlyDays);
    }
}
                                        </div>                </div>                <h4>3.1.3 成本预警机制</h4>                <table>                    <thead>                        <tr>                            <th>预警类型</th>                            <th>触发条件</th>                            <th>预警级别</th>                            <th>处理措施</th>                            <th>通知对象</th>                        </tr>                    </thead>                    <tbody>                        <tr>                            <td>成本异常波动</td>                            <td>成本差异率 > 10%</td>                            <td>高</td>                            <td>立即分析原因，制定应对措施</td>                            <td>财务经理、仓库经理</td>                        </tr>                        <tr>                            <td>超标准成本</td>                            <td>实际成本 > 标准成本 × 1.05</td>                            <td>中</td>                            <td>成本分析，优化作业流程</td>                            <td>成本会计、部门主管</td>                        </tr>                        <tr>                            <td>仓储成本超标</td>                            <td>月度仓储成本 > 预算 × 1.1</td>                            <td>高</td>                            <td>库位优化，提高周转率</td>                            <td>仓库经理、财务总监</td>                        </tr>                        <tr>                            <td>损耗成本超标</td>                            <td>损耗率 > 0.5%</td>                            <td>中</td>                            <td>加强作业管理，改进包装</td>                            <td>质量经理、仓库主管</td>                        </tr>                    </tbody>                </table>            </div>            <h3>3.2 成本核算UI界面设计</h3>            <div class="ui-design-section">                <h4>💰 成本分析仪表板</h4>                <div class="ui-component">                    <div style="background-color: #ffd700; color: white; padding: 10px; margin: -15px -15px 15px -15px; border-radius: 4px 4px 0 0;">                        <h5 style="margin: 0;"><i class="fas fa-chart-line"></i> 成本分析中心</h5>                    </div>                    <!-- 成本概览卡片 -->                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px;">                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center;">                            <div style="font-size: 14px; opacity: 0.9;">本月总成本</div>                            <div style="font-size: 28px; font-weight: bold; margin: 10px 0;">¥1,256,800</div>                            <div style="font-size: 12px;">                                <span style="color: #4ade80;">↗ 5.2%</span> 环比上月                            </div>                        </div>                        <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 8px; text-align: center;">                            <div style="font-size: 14px; opacity: 0.9;">平均库存成本</div>                            <div style="font-size: 28px; font-weight: bold; margin: 10px 0;">¥85.60</div>                            <div style="font-size: 12px;">                                <span style="color: #ef4444;">↘ 2.1%</span> 环比上月                            </div>                        </div>                        <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 20px; border-radius: 8px; text-align: center;">                            <div style="font-size: 14px; opacity: 0.9;">仓储成本率</div>                            <div style="font-size: 28px; font-weight: bold; margin: 10px 0;">3.2%</div>                            <div style="font-size: 12px;">                                <span style="color: #4ade80;">↗ 0.3%</span> 环比上月                            </div>                        </div>                        <div style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; padding: 20px; border-radius: 8px; text-align: center;">                            <div style="font-size: 14px; opacity: 0.9;">成本节约</div>                            <div style="font-size: 28px; font-weight: bold; margin: 10px 0;">¥126,500</div>                            <div style="font-size: 12px;">                                本月优化成果                            </div>                        </div>                    </div>                    <!-- 成本构成分析 -->                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">                        <div style="border: 1px solid #dee2e6; border-radius: 8px; padding: 15px;">                            <h6 style="margin-bottom: 15px;">📊 成本构成分析</h6>                            <div style="height: 300px; background-color: #f8f9fa; border-radius: 4px; display: flex; align-items: center; justify-content: center;">                                <div style="text-align: center;">                                    <div style="font-size: 60px; margin-bottom: 10px;">🥧</div>                                    <div>成本构成饼图</div>                                    <div style="margin-top: 20px; font-size: 14px; color: #6c757d;">                                        <div>采购成本: 75%</div>                                        <div>仓储成本: 15%</div>                                        <div>作业成本: 8%</div>                                        <div>其他成本: 2%</div>                                    </div>                                </div>                            </div>                        </div>                        <div style="border: 1px solid #dee2e6; border-radius: 8px; padding: 15px;">                            <h6 style="margin-bottom: 15px;">📈 成本趋势分析</h6>                            <div style="height: 300px; background-color: #f8f9fa; border-radius: 4px; display: flex; align-items: center; justify-content: center;">                                <div style="text-align: center;">                                    <div style="font-size: 60px; margin-bottom: 10px;">📈</div>                                    <div>成本趋势折线图</div>                                    <div style="margin-top: 20px; font-size: 14px; color: #6c757d;">                                        显示最近12个月成本变化趋势                                    </div>                                </div>                            </div>                        </div>                    </div>                    <!-- 成本明细表格 -->                    <div style="border: 1px solid #dee2e6; border-radius: 8px; padding: 15px;">                        <h6 style="margin-bottom: 15px;">📋 成本明细分析</h6>                        <table style="width: 100%; border-collapse: collapse;">                            <thead style="background-color: #f8f9fa;">                                <tr>                                    <th style="padding: 12px; text-align: left;">物料编码</th>                                    <th style="padding: 12px; text-align: left;">物料名称</th>                                    <th style="padding: 12px; text-align: right;">标准成本</th>                                    <th style="padding: 12px; text-align: right;">实际成本</th>                                    <th style="padding: 12px; text-align: right;">成本差异</th>                                    <th style="padding: 12px; text-align: right;">差异率</th>                                    <th style="padding: 12px; text-align: center;">状态</th>                                    <th style="padding: 12px; text-align: center;">操作</th>                                </tr>                            </thead>                            <tbody>                                <tr style="border-bottom: 1px solid #dee2e6;">                                    <td style="padding: 12px;">MAT001</td>                                    <td style="padding: 12px;">钢板A型</td>                                    <td style="padding: 12px; text-align: right;">¥850.00</td>                                    <td style="padding: 12px; text-align: right;">¥892.50</td>                                    <td style="padding: 12px; text-align: right; color: #dc3545;">¥42.50</td>                                    <td style="padding: 12px; text-align: right; color: #dc3545;">5.0%</td>                                    <td style="padding: 12px; text-align: center;">                                        <span style="background-color: #ffc107; color: black; padding: 4px 8px; border-radius: 12px; font-size: 12px;">超标</span>                                    </td>                                    <td style="padding: 12px; text-align: center;">                                        <button style="background-color: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px;">分析</button>                                    </td>                                </tr>                                <tr style="border-bottom: 1px solid #dee2e6;">                                    <td style="padding: 12px;">MAT002</td>                                    <td style="padding: 12px;">螺栓M8</td>                                    <td style="padding: 12px; text-align: right;">¥2.50</td>                                    <td style="padding: 12px; text-align: right;">¥2.45</td>                                    <td style="padding: 12px; text-align: right; color: #28a745;">-¥0.05</td>                                    <td style="padding: 12px; text-align: right; color: #28a745;">-2.0%</td>                                    <td style="padding: 12px; text-align: center;">                                        <span style="background-color: #28a745; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">正常</span>                                    </td>                                    <td style="padding: 12px; text-align: center;">                                        <button style="background-color: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px;">分析</button>                                    </td>                                </tr>                            </tbody>                        </table>                    </div>                </div>            </div>        </section>        <section id="modules">            <h2>4. 功能模块详细设计</h2>                        <h3>4.1 仓库管理模块</h3>            <div class="module-card">                <h4>4.1.1 仓库基础信息管理</h4>                <table>                    <thead>                        <tr>                            <th>功能名称</th>                            <th>功能描述</th>                            <th>业务规则</th>                            <th>成本相关</th>                            <th>技术实现</th>                        </tr>                    </thead>                    <tbody>                        <tr>                            <td>仓库信息维护</td>                            <td>维护仓库基本信息、面积、容量、成本参数</td>                            <td>仓库编码唯一，面积容量必填</td>                            <td><span class="highlight">月度租金、人工成本、运营费用</span></td>                            <td>Warehouse实体CRUD操作</td>                        </tr>                        <tr>                            <td>库区管理</td>                            <td>划分仓库功能区域，设置存储类型</td>                            <td>库区编码唯一，关联仓库</td>                            <td>按面积分摊仓储成本</td>                            <td>WarehouseArea实体管理</td>                        </tr>                        <tr>                            <td>库位管理</td>                            <td>定义货架、库位，设置存储规则</td>                            <td>库位编码唯一，支持ABC分类</td>                            <td>库位使用成本核算</td>                            <td>Location实体，支持批量导入</td>                        </tr>                    </tbody>                </table>                <div class="ui-design-section">                    <h5>🏭 仓库信息管理界面</h5>                    <div class="ui-component">                        <div style="background-color: #3498db; color: white; padding: 10px; margin: -15px -15px 15px -15px;">                            <h6 style="margin: 0;">仓库基础信息维护</h6>                        </div>                                                <!-- 查询条件区 -->                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px;">                            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px;">                                <div>                                    <label style="display: block; margin-bottom: 5px; font-size: 14px;">仓库编码：</label>                                    <input type="text" placeholder="请输入仓库编码" style="width: 100%; padding: 8px; border: 1px solid #dee2e6; border-radius: 4px;">                                </div>                                <div>                                    <label style="display: block; margin-bottom: 5px; font-size: 14px;">仓库名称：</label>                                    <input type="text" placeholder="请输入仓库名称" style="width: 100%; padding: 8px; border: 1px solid #dee2e6; border-radius: 4px;">                                </div>                                <div>                                    <label style="display: block; margin-bottom: 5px; font-size: 14px;">仓库类型：</label>                                    <select style="width: 100%; padding: 8px; border: 1px solid #dee2e6; border-radius: 4px;">                                        <option>全部</option>                                        <option>普通仓库</option>                                        <option>恒温仓库</option>                                        <option>冷藏仓库</option>                                    </select>                                </div>                                <div style="display: flex; align-items: flex-end; gap: 10px;">                                    <button style="background-color: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px;">查询</button>                                    <button style="background-color: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px;">重置</button>                                    <button style="background-color: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px;">新增</button>                                </div>                            </div>                        </div>                        <!-- 数据表格区 -->                        <div style="border: 1px solid #dee2e6; border-radius: 4px; overflow: hidden;">                            <table style="width: 100%; margin: 0;">                                <thead style="background-color: #e9ecef;">                                    <tr>                                        <th style="padding: 12px; text-align: center;">序号</th>                                        <th style="padding: 12px;">仓库编码</th>                                        <th style="padding: 12px;">仓库名称</th>                                        <th style="padding: 12px;">仓库类型</th>                                        <th style="padding: 12px; text-align: right;">总面积(㎡)</th>                                        <th style="padding: 12px; text-align: right;">月租金(元)</th>                                        <th style="padding: 12px; text-align: right;">人工成本(元/月)</th>                                        <th style="padding: 12px; text-align: center;">状态</th>                                        <th style="padding: 12px; text-align: center;">操作</th>                                    </tr>                                </thead>                                <tbody>                                    <tr style="border-bottom: 1px solid #dee2e6;">                                        <td style="padding: 12px; text-align: center;">1</td>                                        <td style="padding: 12px;">WH001</td>                                        <td style="padding: 12px;">华东中心仓</td>                                        <td style="padding: 12px;">普通仓库</td>                                        <td style="padding: 12px; text-align: right;">5000</td>                                        <td style="padding: 12px; text-align: right; color: #dc3545;">180,000</td>                                        <td style="padding: 12px; text-align: right; color: #dc3545;">250,000</td>                                        <td style="padding: 12px; text-align: center;">                                            <span style="background-color: #28a745; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">启用</span>                                        </td>                                        <td style="padding: 12px; text-align: center;">                                            <button style="background-color: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px; margin-right: 5px;">编辑</button>                                            <button style="background-color: #ffc107; color: black; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px; margin-right: 5px;">成本</button>                                            <button style="background-color: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px;">停用</button>                                        </td>                                    </tr>                                </tbody>                            </table>                        </div>                    </div>                </div>            </div>            <h3>4.2 入库管理模块</h3>            <div class="module-card">                <h4>4.2.1 入库作业流程</h4>                <div class="process-flow">                    <h5>入库作业流程图</h5>                    <div style="text-align: center; padding: 20px;">                        <div style="display: flex; justify-content: center; align-items: center; gap: 30px;">                            <div style="background-color: #e3f2fd; padding: 15px 25px; border-radius: 8px; border: 2px solid #2196f3;">                                <strong>入库通知</strong>                            </div>                            <span style="font-size: 24px;">→</span>                            <div style="background-color: #fff3e0; padding: 15px 25px; border-radius: 8px; border: 2px solid #ff9800;">                                <strong>到货接收</strong>                            </div>                            <span style="font-size: 24px;">→</span>                            <div style="background-color: #f3e5f5; padding: 15px 25px; border-radius: 8px; border: 2px solid #9c27b0;">                                <strong>质量检验</strong>                            </div>                            <span style="font-size: 24px;">→</span>                            <div style="background-color: #e8f5e9; padding: 15px 25px; border-radius: 8px; border: 2px solid #4caf50;">                                <strong>上架入库</strong>                            </div>                            <span style="font-size: 24px;">→</span>                            <div style="background-color: #fce4ec; padding: 15px 25px; border-radius: 8px; border: 2px solid #e91e63;">                                <strong>成本核算</strong>                            </div>                        </div>                    </div>                </div>                <div class="ui-design-section">                    <h5>📥 入库作业界面</h5>                    <div class="ui-component">                        <div style="background-color: #28a745; color: white; padding: 10px; margin: -15px -15px 15px -15px;">                            <h6 style="margin: 0;">入库作业管理</h6>                        </div>                        <!-- 入库单信息 -->                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px;">                            <h6 style="margin-bottom: 15px; color: #495057;">📋 入库单信息</h6>                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">                                <div>                                    <label style="display: block; margin-bottom: 5px; font-size: 14px; font-weight: bold;">入库单号：</label>                                    <div style="font-size: 16px; color: #007bff;">IN202401150001</div>                                </div>                                <div>                                    <label style="display: block; margin-bottom: 5px; font-size: 14px; font-weight: bold;">供应商：</label>                                    <div style="font-size: 16px;">上海钢铁有限公司</div>                                </div>                                <div>                                    <label style="display: block; margin-bottom: 5px; font-size: 14px; font-weight: bold;">预计到货时间：</label>                                    <div style="font-size: 16px;">2024-01-15 14:00</div>                                </div>                            </div>                        </div>                        <!-- 物料明细 -->                        <div style="background-color: #fff; padding: 15px; border: 1px solid #dee2e6; border-radius: 4px; margin-bottom: 20px;">                            <h6 style="margin-bottom: 15px; color: #495057;">📦 物料明细</h6>                            <table style="width: 100%; margin: 0;">                                <thead style="background-color: #f8f9fa;">                                    <tr>                                        <th style="padding: 10px;">物料编码</th>                                        <th style="padding: 10px;">物料名称</th>                                        <th style="padding: 10px;">规格型号</th>                                        <th style="padding: 10px; text-align: right;">计划数量</th>                                        <th style="padding: 10px; text-align: right;">实收数量</th>                                        <th style="padding: 10px; text-align: right;">单价(元)</th>                                        <th style="padding: 10px; text-align: right;">金额(元)</th>                                        <th style="padding: 10px; text-align: center;">质检状态</th>                                        <th style="padding: 10px; text-align: center;">操作</th>                                    </tr>                                </thead>                                <tbody>                                    <tr style="border-bottom: 1px solid #dee2e6;">                                        <td style="padding: 10px;">MAT001</td>                                        <td style="padding: 10px;">钢板A型</td>                                        <td style="padding: 10px;">1200×2400×3mm</td>                                        <td style="padding: 10px; text-align: right;">100</td>                                        <td style="padding: 10px; text-align: right;">                                            <input type="number" value="100" style="width: 80px; padding: 4px; border: 1px solid #dee2e6; border-radius: 3px; text-align: right;">                                        </td>                                        <td style="padding: 10px; text-align: right; color: #dc3545;">850.00</td>                                        <td style="padding: 10px; text-align: right; color: #dc3545; font-weight: bold;">85,000.00</td>                                        <td style="padding: 10px; text-align: center;">                                            <span style="background-color: #ffc107; color: black; padding: 4px 8px; border-radius: 12px; font-size: 12px;">待检</span>                                        </td>                                        <td style="padding: 10px; text-align: center;">                                            <button style="background-color: #9c27b0; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 12px;">质检</button>                                        </td>                                    </tr>                                </tbody>                            </table>                        </div>                        <!-- 成本核算区 -->                        <div style="background-color: #fff8dc; padding: 15px; border: 2px solid #ffd700; border-radius: 4px; margin-bottom: 20px;">                            <h6 style="margin-bottom: 15px; color: #495057;">💰 入库成本核算</h6>                            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px;">                                <div>                                    <label style="display: block; margin-bottom: 5px; font-size: 14px;">物料成本：</label>                                    <div style="font-size: 20px; font-weight: bold; color: #dc3545;">¥85,000.00</div>                                </div>                                <div>                                    <label style="display: block; margin-bottom: 5px; font-size: 14px;">运输费用：</label>                                    <div style="font-size: 20px; font-weight: bold; color: #ff9800;">¥2,500.00</div>                                </div>                                <div>                                    <label style="display: block; margin-bottom: 5px; font-size: 14px;">作业成本：</label>                                    <div style="font-size: 20px; font-weight: bold; color: #ff9800;">¥350.00</div>                                </div>                                <div>                                    <label style="display: block; margin-bottom: 5px; font-size: 14px;">总成本：</label>                                    <div style="font-size: 24px; font-weight: bold; color: #d32f2f;">¥87,850.00</div>                                </div>                            </div>                        </div>                        <!-- 操作按钮 -->                        <div style="text-align: center; padding: 20px;">                            <button style="background-color: #28a745; color: white; border: none; padding: 12px 30px; border-radius: 4px; font-size: 16px; margin-right: 10px;">确认入库</button>                            <button style="background-color: #ffc107; color: black; border: none; padding: 12px 30px; border-radius: 4px; font-size: 16px; margin-right: 10px;">暂存</button>                            <button style="background-color: #6c757d; color: white; border: none; padding: 12px 30px; border-radius: 4px; font-size: 16px;">取消</button>                        </div>                    </div>                </div>            </div>        </section>

        <!-- 添加出库管理模块 -->
        <h3>4.3 出库管理模块</h3>
        <div class="module-card">
            <h4>4.3.1 出库作业流程</h4>
            <div class="process-flow">
                <h5>出库作业流程图</h5>
                <div style="text-align: center; padding: 20px;">
                    <div style="display: flex; justify-content: center; align-items: center; gap: 30px;">
                        <div style="background-color: #e3f2fd; padding: 15px 25px; border-radius: 8px; border: 2px solid #2196f3;">
                            <strong>出库申请</strong>
                        </div>
                        <span style="font-size: 24px;">→</span>
                        <div style="background-color: #fff3e0; padding: 15px 25px; border-radius: 8px; border: 2px solid #ff9800;">
                            <strong>库存分配</strong>
                        </div>
                        <span style="font-size: 24px;">→</span>
                        <div style="background-color: #f3e5f5; padding: 15px 25px; border-radius: 8px; border: 2px solid #9c27b0;">
                            <strong>拣货作业</strong>
                        </div>
                        <span style="font-size: 24px;">→</span>
                        <div style="background-color: #e8f5e9; padding: 15px 25px; border-radius: 8px; border: 2px solid #4caf50;">
                            <strong>复核包装</strong>
                        </div>
                        <span style="font-size: 24px;">→</span>
                        <div style="background-color: #fce4ec; padding: 15px 25px; border-radius: 8px; border: 2px solid #e91e63;">
                            <strong>成本结转</strong>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ui-design-section">
                <h5>📤 出库作业界面</h5>
                <div class="ui-component">
                    <div style="background-color: #dc3545; color: white; padding: 10px; margin: -15px -15px 15px -15px;">
                        <h6 style="margin: 0;">出库作业管理</h6>
                    </div>

                    <!-- 出库单信息 -->
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px;">
                        <h6 style="margin-bottom: 15px; color: #495057;">📋 出库单信息</h6>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-size: 14px; font-weight: bold;">出库单号：</label>
                                <div style="font-size: 16px; color: #dc3545;">OUT202401150001</div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-size: 14px; font-weight: bold;">客户名称：</label>
                                <div style="font-size: 16px;">江苏建筑工程有限公司</div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-size: 14px; font-weight: bold;">出库类型：</label>
                                <div style="font-size: 16px;">销售出库</div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-size: 14px; font-weight: bold;">要求发货时间：</label>
                                <div style="font-size: 16px;">2024-01-15 16:00</div>
                            </div>
                        </div>
                    </div>

                    <!-- 拣货任务分配 -->
                    <div style="background-color: #fff; padding: 15px; border: 1px solid #dee2e6; border-radius: 4px; margin-bottom: 20px;">
                        <h6 style="margin-bottom: 15px; color: #495057;">🎯 拣货任务分配</h6>
                        <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 20px;">
                            <div style="border-right: 1px solid #dee2e6; padding-right: 20px;">
                                <h6 style="margin-bottom: 10px;">待分配任务</h6>
                                <div style="background-color: #f8f9fa; padding: 10px; border-radius: 4px; margin-bottom: 10px;">
                                    <div style="font-weight: bold;">任务编号：PICK001</div>
                                    <div style="font-size: 14px; color: #6c757d;">物料数量：5种，共120件</div>
                                    <div style="font-size: 14px; color: #6c757d;">预计耗时：45分钟</div>
                                    <button style="background-color: #007bff; color: white; border: none; padding: 4px 12px; border-radius: 3px; font-size: 12px; margin-top: 5px;">分配</button>
                                </div>
                            </div>
                            <div>
                                <h6 style="margin-bottom: 10px;">拣货人员状态</h6>
                                <table style="width: 100%;">
                                    <thead style="background-color: #f8f9fa;">
                                        <tr>
                                            <th style="padding: 8px;">员工姓名</th>
                                            <th style="padding: 8px;">当前任务</th>
                                            <th style="padding: 8px;">完成进度</th>
                                            <th style="padding: 8px;">状态</th>
                                            <th style="padding: 8px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr style="border-bottom: 1px solid #dee2e6;">
                                            <td style="padding: 8px;">张三</td>
                                            <td style="padding: 8px;">PICK002</td>
                                            <td style="padding: 8px;">
                                                <div style="background-color: #e9ecef; border-radius: 10px; overflow: hidden;">
                                                    <div style="background-color: #28a745; width: 75%; height: 20px; text-align: center; color: white; line-height: 20px;">75%</div>
                                                </div>
                                            </td>
                                            <td style="padding: 8px;">
                                                <span style="background-color: #ffc107; color: black; padding: 2px 6px; border-radius: 10px; font-size: 12px;">拣货中</span>
                                            </td>
                                            <td style="padding: 8px;">
                                                <button style="background-color: #6c757d; color: white; border: none; padding: 2px 8px; border-radius: 3px; font-size: 12px;">查看</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 出库成本核算 -->
                    <div style="background-color: #fff8dc; padding: 15px; border: 2px solid #ffd700; border-radius: 4px; margin-bottom: 20px;">
                        <h6 style="margin-bottom: 15px; color: #495057;">💰 出库成本核算</h6>
                        <table style="width: 100%;">
                            <thead style="background-color: #ffeaa7;">
                                <tr>
                                    <th style="padding: 10px;">物料编码</th>
                                    <th style="padding: 10px;">物料名称</th>
                                    <th style="padding: 10px; text-align: right;">出库数量</th>
                                    <th style="padding: 10px; text-align: right;">库存成本</th>
                                    <th style="padding: 10px; text-align: right;">销售单价</th>
                                    <th style="padding: 10px; text-align: right;">成本小计</th>
                                    <th style="padding: 10px; text-align: right;">销售小计</th>
                                    <th style="padding: 10px; text-align: right;">毛利</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="border-bottom: 1px solid #dee2e6;">
                                    <td style="padding: 10px;">MAT001</td>
                                    <td style="padding: 10px;">钢板A型</td>
                                    <td style="padding: 10px; text-align: right;">50</td>
                                    <td style="padding: 10px; text-align: right;">¥878.50</td>
                                    <td style="padding: 10px; text-align: right;">¥1,050.00</td>
                                    <td style="padding: 10px; text-align: right; color: #dc3545;">¥43,925.00</td>
                                    <td style="padding: 10px; text-align: right; color: #28a745;">¥52,500.00</td>
                                    <td style="padding: 10px; text-align: right; color: #007bff; font-weight: bold;">¥8,575.00</td>
                                </tr>
                            </tbody>
                            <tfoot style="background-color: #ffeaa7; font-weight: bold;">
                                <tr>
                                    <td colspan="5" style="padding: 10px; text-align: right;">合计：</td>
                                    <td style="padding: 10px; text-align: right; color: #dc3545;">¥43,925.00</td>
                                    <td style="padding: 10px; text-align: right; color: #28a745;">¥52,500.00</td>
                                    <td style="padding: 10px; text-align: right; color: #007bff;">¥8,575.00</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加库存管理模块 -->
        <h3>4.4 库存管理模块</h3>
        <div class="module-card">
            <h4>4.4.1 库存监控与分析</h4>
            
            <div class="ui-design-section">
                <h5>📊 库存监控仪表板</h5>
                <div class="ui-component">
                    <div style="background-color: #17a2b8; color: white; padding: 10px; margin: -15px -15px 15px -15px;">
                        <h6 style="margin: 0;">库存实时监控中心</h6>
                    </div>

                    <!-- 库存概览卡片 -->
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px;">
                        <div style="background-color: #e3f2fd; padding: 15px; border-radius: 8px; border-left: 4px solid #2196f3;">
                            <div style="font-size: 14px; color: #666;">总库存价值</div>
                            <div style="font-size: 24px; font-weight: bold; color: #2196f3;">¥8,562,450</div>
                            <div style="font-size: 12px; color: #999;">较上月 <span style="color: #4caf50;">↑12.5%</span></div>
                        </div>
                        <div style="background-color: #f3e5f5; padding: 15px; border-radius: 8px; border-left: 4px solid #9c27b0;">
                            <div style="font-size: 14px; color: #666;">库存周转率</div>
                            <div style="font-size: 24px; font-weight: bold; color: #9c27b0;">6.8次/年</div>
                            <div style="font-size: 12px; color: #999;">行业平均：5.2次</div>
                        </div>
                        <div style="background-color: #fff3e0; padding: 15px; border-radius: 8px; border-left: 4px solid #ff9800;">
                            <div style="font-size: 14px; color: #666;">呆滞库存</div>
                            <div style="font-size: 24px; font-weight: bold; color: #ff9800;">¥125,600</div>
                            <div style="font-size: 12px; color: #999;">占比 <span style="color: #f44336;">1.47%</span></div>
                        </div>
                        <div style="background-color: #e8f5e9; padding: 15px; border-radius: 8px; border-left: 4px solid #4caf50;">
                            <div style="font-size: 14px; color: #666;">库位利用率</div>
                            <div style="font-size: 24px; font-weight: bold; color: #4caf50;">82.3%</div>
                            <div style="font-size: 12px; color: #999;">可用库位：876个</div>
                        </div>
                    </div>

                    <!-- 库存预警区 -->
                    <div style="background-color: #ffebee; padding: 15px; border-radius: 4px; border: 1px solid #ffcdd2; margin-bottom: 20px;">
                        <h6 style="margin-bottom: 15px; color: #c62828;">⚠️ 库存预警</h6>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                            <div style="background-color: white; padding: 10px; border-radius: 4px; border-left: 3px solid #f44336;">
                                <div style="font-weight: bold; color: #f44336;">低库存预警</div>
                                <div style="font-size: 14px; margin-top: 5px;">
                                    <div>MAT005 - 螺母M12：当前库存 200，安全库存 500</div>
                                    <div>MAT008 - 垫片10mm：当前库存 150，安全库存 300</div>
                                </div>
                            </div>
                            <div style="background-color: white; padding: 10px; border-radius: 4px; border-left: 3px solid #ff9800;">
                                <div style="font-weight: bold; color: #ff9800;">即将过期</div>
                                <div style="font-size: 14px; margin-top: 5px;">
                                    <div>MAT012 - 防锈剂A型：剩余30天，库存50瓶</div>
                                    <div>MAT015 - 密封胶B型：剩余45天，库存80支</div>
                                </div>
                            </div>
                            <div style="background-color: white; padding: 10px; border-radius: 4px; border-left: 3px solid #ffc107;">
                                <div style="font-weight: bold; color: #ffc107;">超期库存</div>
                                <div style="font-size: 14px; margin-top: 5px;">
                                    <div>MAT020 - 特殊钢材：库龄180天，价值¥45,000</div>
                                    <div>MAT022 - 定制配件：库龄150天，价值¥28,000</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 库存ABC分析 -->
                    <div style="background-color: #fff; padding: 15px; border: 1px solid #dee2e6; border-radius: 4px;">
                        <h6 style="margin-bottom: 15px; color: #495057;">📈 库存ABC分析</h6>
                        <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 20px;">
                            <div>
                                <div style="text-align: center; padding: 20px;">
                                    <div style="font-size: 60px;">📊</div>
                                    <div>ABC分类占比图</div>
                                    <div style="margin-top: 20px;">
                                        <div style="background-color: #f44336; color: white; padding: 5px; margin: 5px 0;">A类：20% 品种，80% 价值</div>
                                        <div style="background-color: #ff9800; color: white; padding: 5px; margin: 5px 0;">B类：30% 品种，15% 价值</div>
                                        <div style="background-color: #4caf50; color: white; padding: 5px; margin: 5px 0;">C类：50% 品种，5% 价值</div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <table style="width: 100%;">
                                    <thead style="background-color: #f8f9fa;">
                                        <tr>
                                            <th style="padding: 10px;">分类</th>
                                            <th style="padding: 10px;">物料编码</th>
                                            <th style="padding: 10px;">物料名称</th>
                                            <th style="padding: 10px; text-align: right;">库存数量</th>
                                            <th style="padding: 10px; text-align: right;">库存价值</th>
                                            <th style="padding: 10px; text-align: center;">管理策略</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr style="border-bottom: 1px solid #dee2e6;">
                                            <td style="padding: 10px;">
                                                <span style="background-color: #f44336; color: white; padding: 2px 8px; border-radius: 3px;">A</span>
                                            </td>
                                            <td style="padding: 10px;">MAT001</td>
                                            <td style="padding: 10px;">钢板A型</td>
                                            <td style="padding: 10px; text-align: right;">500</td>
                                            <td style="padding: 10px; text-align: right; color: #f44336; font-weight: bold;">¥439,250</td>
                                            <td style="padding: 10px; text-align: center;">
                                                <span style="font-size: 12px; color: #666;">重点管控</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加盘点管理模块 -->
        <h3>4.5 盘点管理模块</h3>
        <div class="module-card">
            <h4>4.5.1 库存盘点作业</h4>
            
            <div class="ui-design-section">
                <h5>📋 盘点作业界面</h5>
                <div class="ui-component">
                    <div style="background-color: #6c757d; color: white; padding: 10px; margin: -15px -15px 15px -15px;">
                        <h6 style="margin: 0;">库存盘点管理</h6>
                    </div>

                    <!-- 盘点计划信息 -->
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px;">
                        <h6 style="margin-bottom: 15px; color: #495057;">📅 盘点计划信息</h6>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-size: 14px;">盘点单号：</label>
                                <div style="font-size: 16px; font-weight: bold;">ST202401150001</div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-size: 14px;">盘点类型：</label>
                                <div style="font-size: 16px;">循环盘点</div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-size: 14px;">盘点范围：</label>
                                <div style="font-size: 16px;">A区-01排-高值物料</div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-size: 14px;">计划时间：</label>
                                <div style="font-size: 16px;">2024-01-15 09:00-12:00</div>
                            </div>
                        </div>
                    </div>

                    <!-- 盘点执行区 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <!-- 待盘点列表 -->
                        <div style="background-color: #fff; padding: 15px; border: 1px solid #dee2e6; border-radius: 4px;">
                            <h6 style="margin-bottom: 15px; color: #495057;">📦 待盘点物料</h6>
                            <div style="max-height: 400px; overflow-y: auto;">
                                <div style="background-color: #f8f9fa; padding: 10px; margin-bottom: 10px; border-radius: 4px; border-left: 3px solid #ffc107;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div>
                                            <div style="font-weight: bold;">MAT001 - 钢板A型</div>
                                            <div style="font-size: 14px; color: #666;">库位：A01-01-01 | 账面数量：100</div>
                                        </div>
                                        <button style="background-color: #007bff; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 12px;">盘点</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 盘点录入区 -->
                        <div style="background-color: #e7f3ff; padding: 15px; border: 2px solid #007bff; border-radius: 4px;">
                            <h6 style="margin-bottom: 15px; color: #0056b3;">✏️ 盘点录入</h6>
                            <div style="background-color: white; padding: 15px; border-radius: 4px;">
                                <div style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">当前盘点物料：</label>
                                    <div style="font-size: 18px; color: #007bff;">MAT001 - 钢板A型</div>
                                </div>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                    <div>
                                        <label style="display: block; margin-bottom: 5px;">账面数量：</label>
                                        <div style="font-size: 24px; font-weight: bold; color: #6c757d;">100</div>
                                    </div>
                                    <div>
                                        <label style="display: block; margin-bottom: 5px;">实盘数量：</label>
                                        <input type="number" style="width: 100%; padding: 10px; font-size: 24px; font-weight: bold; border: 2px solid #007bff; border-radius: 4px; text-align: center;" placeholder="0">
                                    </div>
                                </div>
                                <div style="margin-top: 15px;">
                                    <label style="display: block; margin-bottom: 5px;">盘点备注：</label>
                                    <textarea style="width: 100%; padding: 8px; border: 1px solid #dee2e6; border-radius: 4px;" rows="3" placeholder="请输入盘点备注..."></textarea>
                                </div>
                                <div style="margin-top: 15px; text-align: center;">
                                    <button style="background-color: #28a745; color: white; border: none; padding: 10px 30px; border-radius: 4px; margin-right: 10px;">确认盘点</button>
                                    <button style="background-color: #6c757d; color: white; border: none; padding: 10px 30px; border-radius: 4px;">跳过</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 盘点差异分析 -->
                    <div style="background-color: #fff8dc; padding: 15px; border: 2px solid #ffd700; border-radius: 4px;">
                        <h6 style="margin-bottom: 15px; color: #495057;">📊 盘点差异分析</h6>
                        <table style="width: 100%;">
                            <thead style="background-color: #ffeaa7;">
                                <tr>
                                    <th style="padding: 10px;">物料编码</th>
                                    <th style="padding: 10px;">物料名称</th>
                                    <th style="padding: 10px; text-align: right;">账面数量</th>
                                    <th style="padding: 10px; text-align: right;">实盘数量</th>
                                    <th style="padding: 10px; text-align: right;">差异数量</th>
                                    <th style="padding: 10px; text-align: right;">差异金额</th>
                                    <th style="padding: 10px; text-align: center;">差异原因</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="border-bottom: 1px solid #dee2e6;">
                                    <td style="padding: 10px;">MAT001</td>
                                    <td style="padding: 10px;">钢板A型</td>
                                    <td style="padding: 10px; text-align: right;">100</td>
                                    <td style="padding: 10px; text-align: right;">98</td>
                                    <td style="padding: 10px; text-align: right; color: #dc3545;">-2</td>
                                    <td style="padding: 10px; text-align: right; color: #dc3545; font-weight: bold;">-¥1,757.00</td>
                                    <td style="padding: 10px; text-align: center;">
                                        <select style="padding: 4px; border: 1px solid #dee2e6; border-radius: 3px;">
                                            <option>请选择</option>
                                            <option>正常损耗</option>
                                            <option>记录错误</option>
                                            <option>其他原因</option>
                                        </select>
                                    </td>
                                </tr>
                            </tbody>
                            <tfoot style="background-color: #ffeaa7; font-weight: bold;">
                                <tr>
                                    <td colspan="5" style="padding: 10px; text-align: right;">盘点差异合计：</td>
                                    <td style="padding: 10px; text-align: right; color: #dc3545;">-¥1,757.00</td>
                                    <td style="padding: 10px; text-align: center;">
                                        <button style="background-color: #dc3545; color: white; border: none; padding: 4px 12px; border-radius: 3px;">生成调整单</button>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="workflow">
        <h2>5. 业务流程详细设计</h2>
        
        <h3>5.1 入库业务流程</h3>
        <div class="process-flow">
            <h4>5.1.1 完整入库流程</h4>
            <table>
                <thead>
                    <tr>
                        <th>流程阶段</th>
                        <th>步骤</th>
                        <th>责任人</th>
                        <th>系统操作</th>
                        <th>成本影响</th>
                        <th>关键控制点</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td rowspan="3">入库准备</td>
                        <td>1. 接收入库通知</td>
                        <td>仓库文员</td>
                        <td>创建入库单，录入预计到货信息</td>
                        <td>-</td>
                        <td>核对采购订单信息</td>
                    </tr>
                    <tr>
                        <td>2. 资源准备</td>
                        <td>仓库主管</td>
                        <td>分配收货月台、准备人员设备</td>
                        <td>人工成本计入</td>
                        <td>资源可用性确认</td>
                    </tr>
                    <tr>
                        <td>3. 供应商预约</td>
                        <td>供应商/仓库文员</td>
                        <td>确认送货时间窗口</td>
                        <td>-</td>
                        <td>时间窗口不冲突</td>
                    </tr>
                    <tr>
                        <td rowspan="4">收货作业</td>
                        <td>4. 车辆到货</td>
                        <td>门卫</td>
                        <td>登记车辆信息，引导至月台</td>
                        <td>-</td>
                        <td>身份验证</td>
                    </tr>
                    <tr>
                        <td>5. 卸货检查</td>
                        <td>收货员</td>
                        <td>外观检查，数量清点</td>
                        <td>作业成本开始计算</td>
                        <td>货物完整性</td>
                    </tr>
                    <tr>
                        <td>6. 扫码收货</td>
                        <td>收货员</td>
                        <td>扫描条码，确认收货数量</td>
                        <td>-</td>
                        <td>数量准确性</td>
                    </tr>
                    <tr>
                        <td>7. 生成收货单</td>
                        <td>系统自动</td>
                        <td>生成收货记录，更新入库单状态</td>
                        <td>记录采购成本</td>
                        <td>数据完整性</td>
                    </tr>
                    <tr>
                        <td rowspan="3">质量检验</td>
                        <td>8. 质检抽样</td>
                        <td>质检员</td>
                        <td>按抽样标准抽取样品</td>
                        <td>质检成本计入</td>
                        <td>抽样代表性</td>
                    </tr>
                    <tr>
                        <td>9. 检验执行</td>
                        <td>质检员</td>
                        <td>执行检验项目，记录结果</td>
                        <td>-</td>
                        <td>检验标准符合性</td>
                    </tr>
                    <tr>
                        <td>10. 质检判定</td>
                        <td>质检主管</td>
                        <td>审核检验结果，做出判定</td>
                        <td>不合格品成本处理</td>
                        <td>判定准确性</td>
                    </tr>
                    <tr>
                        <td rowspan="3">上架入库</td>
                        <td>11. 库位分配</td>
                        <td>系统自动</td>
                        <td>根据规则分配最优库位</td>
                        <td>-</td>
                        <td>库位适配性</td>
                    </tr>
                    <tr>
                        <td>12. 上架作业</td>
                        <td>上架员</td>
                        <td>按指定库位进行上架</td>
                        <td>作业成本计入</td>
                        <td>库位准确性</td>
                    </tr>
                    <tr>
                        <td>13. 入库确认</td>
                        <td>上架员</td>
                        <td>扫码确认上架完成</td>
                        <td>-</td>
                        <td>库存更新准确性</td>
                    </tr>
                    <tr>
                        <td rowspan="2">成本核算</td>
                        <td>14. 成本计算</td>
                        <td>系统自动</td>
                        <td>计算总入库成本</td>
                        <td>更新库存成本</td>
                        <td>成本要素完整性</td>
                    </tr>
                    <tr>
                        <td>15. 成本分摊</td>
                        <td>系统自动</td>
                        <td>按加权平均法更新单位成本</td>
                        <td>影响后续出库成本</td>
                        <td>计算准确性</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h3>5.2 出库业务流程</h3>
        <div class="process-flow">
            <h4>5.2.1 完整出库流程</h4>
            <table>
                <thead>
                    <tr>
                        <th>流程阶段</th>
                        <th>步骤</th>
                        <th>责任人</th>
                        <th>系统操作</th>
                        <th>成本影响</th>
                        <th>关键控制点</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td rowspan="3">出库准备</td>
                        <td>1. 接收出库申请</td>
                        <td>销售/生产部门</td>
                        <td>创建出库申请单</td>
                        <td>-</td>
                        <td>申请合理性</td>
                    </tr>
                    <tr>
                        <td>2. 库存检查</td>
                        <td>系统自动</td>
                        <td>检查库存可用性</td>
                        <td>-</td>
                        <td>库存充足性</td>
                    </tr>
                    <tr>
                        <td>3. 出库审批</td>
                        <td>仓库主管</td>
                        <td>审批出库申请</td>
                        <td>-</td>
                        <td>权限控制</td>
                    </tr>
                    <tr>
                        <td rowspan="3">库存分配</td>
                        <td>4. 批次选择</td>
                        <td>系统自动</td>
                        <td>按FIFO原则选择批次</td>
                        <td>确定出库成本</td>
                        <td>批次准确性</td>
                    </tr>
                    <tr>
                        <td>5. 库位锁定</td>
                        <td>系统自动</td>
                        <td>锁定分配的库存</td>
                        <td>-</td>
                        <td>防止重复分配</td>
                    </tr>
                    <tr>
                        <td>6. 生成拣货单</td>
                        <td>系统自动</td>
                        <td>优化拣货路径</td>
                        <td>-</td>
                        <td>路径最优化</td>
                    </tr>
                    <tr>
                        <td rowspan="3">拣货作业</td>
                        <td>7. 任务分配</td>
                        <td>拣货主管</td>
                        <td>分配拣货任务给作业人员</td>
                        <td>人工成本计入</td>
                        <td>负载均衡</td>
                    </tr>
                    <tr>
                        <td>8. 拣货执行</td>
                        <td>拣货员</td>
                        <td>按拣货单进行拣货</td>
                        <td>作业成本计入</td>
                        <td>拣货准确性</td>
                    </tr>
                    <tr>
                        <td>9. 拣货确认</td>
                        <td>拣货员</td>
                        <td>扫码确认拣货完成</td>
                        <td>-</td>
                        <td>数量核对</td>
                    </tr>
                    <tr>
                        <td rowspan="2">复核包装</td>
                        <td>10. 出库复核</td>
                        <td>复核员</td>
                        <td>核对物料和数量</td>
                        <td>-</td>
                        <td>准确性验证</td>
                    </tr>
                    <tr>
                        <td>11. 包装发货</td>
                        <td>包装员</td>
                        <td>包装并贴标签</td>
                        <td>包装成本计入</td>
                        <td>包装完整性</td>
                    </tr>
                    <tr>
                        <td rowspan="2">成本结转</td>
                        <td>12. 成本计算</td>
                        <td>系统自动</td>
                        <td>计算出库成本</td>
                        <td>结转库存成本</td>
                        <td>成本准确性</td>
                    </tr>
                    <tr>
                        <td>13. 更新库存</td>
                        <td>系统自动</td>
                        <td>扣减库存，更新成本</td>
                        <td>影响库存价值</td>
                        <td>数据一致性</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>

    <section id="validation">
        <h2>7. 业务流程逻辑完整性校验</h2>
        
        <div class="validation-box">
            <h3>7.1 流程完整性验证</h3>
            
            <h4>7.1.1 入库流程验证</h4>
            <table>
                <thead>
                    <tr>
                        <th>验证项</th>
                        <th>验证内容</th>
                        <th>验证结果</th>
                        <th>改进建议</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>流程闭环性</td>
                        <td>从入库申请到成本核算的完整闭环</td>
                        <td>✅ 通过</td>
                        <td>流程完整，各环节衔接顺畅</td>
                    </tr>
                    <tr>
                        <td>数据一致性</td>
                        <td>各环节数据传递的准确性和完整性</td>
                        <td>✅ 通过</td>
                        <td>建议增加数据校验点</td>
                    </tr>
                    <tr>
                        <td>成本完整性</td>
                        <td>所有成本要素是否都已计入</td>
                        <td>✅ 通过</td>
                        <td>成本要素齐全，计算准确</td>
                    </tr>
                    <tr>
                        <td>异常处理</td>
                        <td>各环节异常情况的处理机制</td>
                        <td>⚠️ 需改进</td>
                        <td>建议完善质检不合格的处理流程</td>
                    </tr>
                </tbody>
            </table>

            <h4>7.1.2 库存管理逻辑验证</h4>
            <table>
                <thead>
                    <tr>
                        <th>验证公式</th>
                        <th>验证说明</th>
                        <th>系统实现</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>期末库存 = 期初库存 + 入库 - 出库 ± 调整</td>
                        <td>库存平衡公式验证</td>
                        <td>系统自动计算并校验</td>
                    </tr>
                    <tr>
                        <td>可用库存 = 实物库存 - 预留库存 - 冻结库存</td>
                        <td>可用库存计算验证</td>
                        <td>实时计算，防止超分配</td>
                    </tr>
                    <tr>
                        <td>库存成本 = Σ(批次数量 × 批次成本) / 总数量</td>
                        <td>加权平均成本验证</td>
                        <td>每次出入库自动重算</td>
                    </tr>
                </tbody>
            </table>

            <h4>7.1.3 成本核算逻辑验证</h4>
            <div class="code-block">
// 成本核算完整性验证
public class CostValidationService
{
    /// <summary>
    /// 验证入库成本完整性
    /// </summary>
    public ValidationResult ValidateInboundCost(InboundOrder order)
    {
        var result = new ValidationResult();
        
        // 1. 验证物料成本
        var materialCost = order.Details.Sum(d => d.Quantity * d.UnitPrice);
        if (materialCost <= 0)
        {
            result.AddError("物料成本不能为零");
        }
        
        // 2. 验证附加成本
        var additionalCost = order.TransportCost + order.TaxCost + order.OtherCost;
        if (additionalCost < 0)
        {
            result.AddError("附加成本不能为负数");
        }
        
        // 3. 验证作业成本
        var operationCost = CalculateOperationCost(order);
        if (operationCost < 0)
        {
            result.AddError("作业成本计算异常");
        }
        
        // 4. 验证总成本
        var totalCost = materialCost + additionalCost + operationCost;
        if (Math.Abs(totalCost - order.TotalCost) > 0.01m)
        {
            result.AddError("总成本计算不一致");
        }
        
        return result;
    }
    
    /// <summary>
    /// 验证库存成本一致性
    /// </summary>
    public ValidationResult ValidateInventoryCost(string materialId)
    {
        var result = new ValidationResult();
        
        // 获取所有批次库存
        var inventories = _inventoryRepo.GetByMaterial(materialId);
        
        // 计算加权平均成本
        var totalQuantity = inventories.Sum(i => i.Quantity);
        var totalCost = inventories.Sum(i => i.Quantity * i.Cost);
        var avgCost = totalQuantity > 0 ? totalCost / totalQuantity : 0;
        
        // 验证与系统记录的平均成本是否一致
        var systemAvgCost = _materialRepo.GetAverageCost(materialId);
        if (Math.Abs(avgCost - systemAvgCost) > 0.01m)
        {
            result.AddError($"库存成本不一致：计算值{avgCost}，系统值{systemAvgCost}");
        }
        
        return result;
    }
}
            </div>
        </div>
    </section>

    <section id="technology">
        <h2>8. 技术架构</h2>
        
        <h3>8.1 系统架构设计</h3>
        <div class="code-block">
// 依赖注入配置 - 包含成本核算服务
public class Startup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // 数据库上下文
        services.AddDbContext<WMSDbContext>(options =>
            options.UseSqlServer(Configuration.GetConnectionString("DefaultConnection")));
        
        // 缓存服务
        services.AddStackExchangeRedisCache(options =>
        {
            options.Configuration = Configuration.GetConnectionString("Redis");
            options.InstanceName = "WMS";
        });
        
        // 核心业务服务
        services.AddScoped<IWarehouseService, WarehouseService>();
        services.AddScoped<IInventoryService, InventoryService>();
        services.AddScoped<IInboundService, InboundService>();
        services.AddScoped<IOutboundService, OutboundService>();
        
        // 成本核算服务
        services.AddScoped<ICostCalculationService, CostCalculationService>();
        services.AddScoped<ICostAnalysisService, CostAnalysisService>();
        services.AddScoped<ICostAllocationService, CostAllocationService>();
        
        // 仓储模式
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
        
        // 后台任务
        services.AddHostedService<CostCalculationBackgroundService>();
        services.AddHostedService<InventoryMonitoringService>();
        
        // AutoMapper
        services.AddAutoMapper(typeof(MappingProfile));
        
        // 认证授权
        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = Configuration["Jwt:Issuer"],
                    ValidAudience = Configuration["Jwt:Audience"],
                    IssuerSigningKey = new SymmetricSecurityKey(
                        Encoding.UTF8.GetBytes(Configuration["Jwt:Key"]))
                };
            });
    }
}

// 成本计算后台服务
public class CostCalculationBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<CostCalculationBackgroundService> _logger;
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using (var scope = _serviceProvider.CreateScope())
                {
                    var costService = scope.ServiceProvider.GetRequiredService<ICostCalculationService>();
                    
                    // 计算仓储成本
                    await costService.CalculateMonthlyStorageCost();
                    
                    // 更新库存成本
                    await costService.UpdateInventoryCost();
                    
                    // 成本异常检测
                    await costService.DetectCostAnomalies();
                }
                
                // 每天执行一次
                await Task.Delay(TimeSpan.FromDays(1), stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "成本计算服务执行失败");
            }
        }
    }
}
        </div>
    </section>

    <section id="implementation">
        <h2>9. 实施方案</h2>
        
        <h3>9.1 项目实施计划</h3>
        <table>
            <thead>
                <tr>
                    <th>阶段</th>
                    <th>主要任务</th>
                    <th>交付成果</th>
                    <th>工期</th>
                    <th>关键里程碑</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>需求分析</td>
                    <td>业务调研、需求确认、成本核算需求细化</td>
                    <td>需求规格说明书、成本核算方案</td>
                    <td>3周</td>
                    <td>需求评审通过</td>
                </tr>
                <tr>
                    <td>系统设计</td>
                    <td>架构设计、数据库设计、UI/UX设计</td>
                    <td>系统设计文档、UI原型</td>
                    <td>2周</td>
                    <td>设计评审通过</td>
                </tr>
                <tr>
                    <td>核心开发</td>
                    <td>基础模块开发、成本核算模块开发</td>
                    <td>核心功能代码、单元测试</td>
                    <td>8周</td>
                    <td>核心功能验收</td>
                </tr>
                <tr>
                    <td>集成测试</td>
                    <td>功能测试、性能测试、安全测试</td>
                    <td>测试报告、缺陷修复</td>
                    <td>3周</td>
                    <td>测试通过率>95%</td>
                </tr>
                <tr>
                    <td>试运行</td>
                    <td>试点上线、用户培训、问题收集</td>
                    <td>培训材料、运行报告</td>
                    <td>2周</td>
                    <td>试运行稳定</td>
                </tr>
                <tr>
                    <td>正式上线</td>
                    <td>全面部署、数据迁移、切换上线</td>
                    <td>上线报告、运维手册</td>
                    <td>1周</td>
                    <td>系统正式运行</td>
                </tr>
            </tbody>
        </table>

        <h3>9.2 成功标准</h3>
        <ul>
            <li><strong>功能完整性</strong>：所有功能模块100%实现，成本核算准确率>99.9%</li>
            <li><strong>性能指标</strong>：页面响应<2秒，并发用户>500，成本计算<5秒</li>
            <li><strong>数据准确性</strong>：库存准确率>99.5%，成本差异率<1%</li>
            <li><strong>用户满意度</strong>：系统易用性评分>4.5/5，培训后独立操作率>90%</li>
            <li><strong>投资回报</strong>：库存周转率提升20%，仓储成本降低15%，作业效率提升30%</li>
        </ul>
    </section>

    <div class="footer" style="margin-top: 50px; padding: 20px; background-color: #f8f9fa; text-align: center;">
        <p><strong>梵素EAP仓库管理系统详细设计方案（增强版）</strong></p>
        <p>版本：3.0 | 日期：2024年1月 | 设计团队：梵素科技</p>
        <p>本文档包含完整的功能模块设计、成本核算功能、详细UI界面设计和业务流程逻辑验证</p>
    </div>
</div>
</body>
</html> 